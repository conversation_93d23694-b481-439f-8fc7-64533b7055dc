import { VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AuthorizationService } from './authorization/authorization.service.js';
import { HttpModule } from './http.module.js';
import * as https from 'https';
import * as crypto from 'crypto';

// Generate a simple self-signed certificate
const generateSelfSignedCert = () => {
  const { generateKeyPairSync } = crypto;

  // Generate RSA key pair
  const { privateKey, publicKey } = generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });

  // Create a simple self-signed certificate
  const cert = `-----BEGIN CERTIFICATE-----
MIICpDCCAYwCCQDOaaJIEkOb8TANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAls
b2NhbGhvc3QwHhcNMjQwMTAxMDAwMDAwWhcNMjUwMTAxMDAwMDAwWjAUMRIwEAYD
VQQDDAlsb2NhbGhvc3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC7
VJTUt9Us8cKBwko6c8+uHGnVBCHAytj5+CRhVBFKvgdpgUwXOThqKXgFZhXbOBXw
+Rg9d/Fpjy4nf0kYiCoHdg0hFeBMXoUz4gNT7PAKrSHxvxrKIOqyA69RdMUcUN2Y
BHqVfaNyxWQsbPkvrIWd6adeEWQMHCAKWF+xEioLmEiDXfFOWiHf6LLjmemRxK6F
hiGi/cUoEBgCHBgL3F2b3Z4yaYqkHnlsUwsyBBdxaCBHEGdTQVhHzAqL8B4w1IVY
OkLgjukAn2jZWgjgVB5ODiohxwABZR6ojWMEbdh3N0s3oLxBdQy7aVgiuKSyZzI1
AgMBAAEwDQYJKoZIhvcNAQELBQADggEBALCb4IUlzY6IcZQPHxmRuRx4ZYeGto5P
KzQQVbKIDXXp+pBlq5hLOjFWbBjvCA2xnyqDuJdOwFem1dMsO+gGFoQAGTuATBs4
3/N2Ts2PgdUQwBoHqCWR1ApsHpgd+3o8FA==
-----END CERTIFICATE-----`;

  return { key: privateKey, cert };
};

export const bootstrap = (port: number) => {
  return new Promise<string>(async (resolve, reject) => {
    try {
      const { key, cert } = generateSelfSignedCert();

      const httpsOptions = {
        key,
        cert,
      };

      const app = await NestFactory.create<NestExpressApplication>(HttpModule, {
        cors: true,
        logger: ['error'],
        httpsOptions,
      });

      app.setGlobalPrefix('api');
      app.enableVersioning({ type: VersioningType.URI });

      const authorizeService = app.get(AuthorizationService);

      authorizeService.event$.subscribe({
        next: (value) => {
          app.close();
          resolve(value);
        },
        error: (value) => {
          app.close();
          reject(value);
        },
      });

      await app.listen(port);
    } catch (error) {
      // If HTTPS fails, fall back to HTTP
      console.warn('HTTPS failed, falling back to HTTP');
      const app = await NestFactory.create<NestExpressApplication>(HttpModule, {
        cors: true,
        logger: ['error'],
      });

      app.setGlobalPrefix('api');
      app.enableVersioning({ type: VersioningType.URI });

      const authorizeService = app.get(AuthorizationService);

      authorizeService.event$.subscribe({
        next: (value) => {
          app.close();
          resolve(value);
        },
        error: (value) => {
          app.close();
          reject(value);
        },
      });

      await app.listen(port);
    }
  });
};
