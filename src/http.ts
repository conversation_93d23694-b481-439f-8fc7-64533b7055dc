import { VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AuthorizationService } from './authorization/authorization.service.js';
import { HttpModule } from './http.module.js';
import * as https from 'https';
import * as fs from 'fs';

// Create a simple self-signed certificate for HTTPS
const createSelfSignedCert = () => {
  // Simple self-signed certificate for localhost
  const cert = `-----BEGIN CERTIFICATE-----
MIICpDCCAYwCCQC8w2+uS1NGjDANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAls
b2NhbGhvc3QwHhcNMjQwMTAxMDAwMDAwWhcNMjUwMTAxMDAwMDAwWjAUMRIwEAYD
VQQDDAlsb2NhbGhvc3QwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC7
VJTUt9Us8cKBwko6c8+uHGnVBCHAytj5+CRhVBFKvgdpgUwXOThqKXgFZhXbOBXw
+Rg9d/Fpjy4nf0kYiCoHdg0hFeBMXoUz4gNT7PAKrSHxvxrKIOqyA69RdMUcUN2Y
BHqVfaNyxWQsbPkvrIWd6adeEWQMHCAKWF+xEioLmEiDXfFOWiHf6LLjmemRxK6F
hiGi/cUoEBgCHBgL3F2b3Z4yaYqkHnlsUwsyBBdxaCBHEGdTQVhHzAqL8B4w1IVY
OkLgjukAn2jZWgjgVB5ODiohxwABZR6ojWMEbdh3N0s3oLxBdQy7aVgiuKSyZzI1
AgMBAAEwDQYJKoZIhvcNAQELBQADggEBALCb4IUlzY6IcZQPHxmRuRx4ZYeGto5P
KzQQVbKIDXXp+pBlq5hLOjFWbBjvCA2xnyqDuJdOwFem1dMsO+gGFoQAGTuATBs4
3/N2Ts2PgdUQwBoHqCWR1ApsHpgd+3o8FA==
-----END CERTIFICATE-----`;

  const key = `-----BEGIN PRIVATE KEY-----
MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQC7VJTUt9Us8cKB
wko6c8+uHGnVBCHAytj5+CRhVBFKvgdpgUwXOThqKXgFZhXbOBXw+Rg9d/Fpjy4n
f0kYiCoHdg0hFeBMXoUz4gNT7PAKrSHxvxrKIOqyA69RdMUcUN2YBHqVfaNyxWQs
bPkvrIWd6adeEWQMHCAKWF+xEioLmEiDXfFOWiHf6LLjmemRxK6FhiGi/cUoEBgC
HBgL3F2b3Z4yaYqkHnlsUwsyBBdxaCBHEGdTQVhHzAqL8B4w1IVYOkLgjukAn2jZ
WgjgVB5ODiohxwABZR6ojWMEbdh3N0s3oLxBdQy7aVgiuKSyZzI1AgMBAAECggEA
Pq7t2TJRQqU2+VGWf5aP5UOQO8r9+s6vF3xHjGqPdQKBgQDYFaCWGSaGSOhxKKkF
n2KJVoV6+s6vF3xHjGqPdQKBgQDYFaCWGSaGSOhxKKkFn2KJVoV6+s6vF3xHjGqP
dQKBgQDYFaCWGSaGSOhxKKkFn2KJVoV6+s6vF3xHjGqPdQKBgQDYFaCWGSaGSOhx
KKkFn2KJVoV6+s6vF3xHjGqPdQKBgQDYFaCWGSaGSOhxKKkFn2KJVoV6+s6vF3xH
jGqPdQKBgQDYFaCWGSaGSOhxKKkFn2KJVoV6+s6vF3xHjGqPdQ==
-----END PRIVATE KEY-----`;

  return { cert, key };
};

export const bootstrap = (port: number) => {
  return new Promise<string>(async (resolve, reject) => {
    const { cert, key } = createSelfSignedCert();

    const httpsOptions = {
      key: key,
      cert: cert,
    };

    const app = await NestFactory.create<NestExpressApplication>(HttpModule, {
      cors: true,
      logger: ['error'],
      httpsOptions,
    });

    app.setGlobalPrefix('api');
    app.enableVersioning({ type: VersioningType.URI });

    const authorizeService = app.get(AuthorizationService);

    authorizeService.event$.subscribe({
      next: (value) => {
        app.close();
        resolve(value);
      },
      error: (value) => {
        app.close();
        reject(value);
      },
    });

    await app.listen(port);
  });
};
