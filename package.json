{"name": "@lyricstify/lyricstify", "version": "1.1.2", "description": "Show synchronized Spotify lyrics in your terminal.", "keywords": ["nodejs", "javascript", "music", "windows", "macos", "linux", "shell", "bash", "zsh", "cli", "fish", "spotify", "typescript", "rxjs", "terminal", "cross-platform", "lyrics", "song", "<PERSON><PERSON><PERSON>", "terminal-kit"], "author": "<PERSON>", "private": false, "license": "MIT", "type": "module", "engines": {"node": ">=16.20.0"}, "scripts": {"build": "nest build", "build:bundle": "esbuild $(esbuild-config ./esbuild.json)", "build:executable": "pkg bundle/bundle.js --config=package.json --compress GZip", "format": "prettier --write \"*.json\" \".github/**/*.yml\" \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start --entry<PERSON>ile cli", "start:watch": "nest start --watch --entryFile cli", "start:debug": "nest start --debug --watch --entryFile cli", "start:repl": "nest start --entryFile repl", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:ci": "eslint \"{src,apps,libs,test}/**/*.ts\" --format github", "test": "node --experimental-vm-modules node_modules/.bin/jest", "test:ci": "node --experimental-vm-modules node_modules/.bin/jest --coverage --runInBand", "test:watch": "jest --watch", "test:cov": "node --experimental-vm-modules node_modules/.bin/jest --coverage", "test:debug": "node --experimental-vm-modules --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:unit": "node --experimental-vm-modules node_modules/.bin/jest ./src", "test:e2e": "node --experimental-vm-modules node_modules/.bin/jest ./test", "prepare": "is-ci || husky install"}, "dependencies": {"@nestjs/axios": "^2.0.0", "@nestjs/common": "^9.0.0", "@nestjs/core": "^9.0.0", "@nestjs/mapped-types": "^1.2.2", "@nestjs/platform-express": "^9.0.0", "axios": "^1.3.4", "chalk": "^5.2.0", "google-translate-api-x": "^10.6.4", "kuroshiro": "~1.1.2", "kuroshiro-analyzer-kuromoji": "^1.1.0", "lowdb": "^5.1.0", "nest-commander": "^3.6.1", "ora": "^6.1.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "terminal-kit": "^3.0.0"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@hirez_io/observer-spy": "^2.2.0", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@semantic-release/changelog": "^6.0.2", "@semantic-release/commit-analyzer": "^9.0.2", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.0.7", "@semantic-release/npm": "^9.0.2", "@semantic-release/release-notes-generator": "^10.0.3", "@types/express": "^4.17.13", "@types/jest": "29.5.3", "@types/kuromoji": "^0.1.1", "@types/node": "18.19.29", "@types/spotify-api": "^0.0.25", "@types/supertest": "^6.0.2", "@types/terminal-kit": "^2.5.1", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "esbuild": "^0.20.0", "esbuild-config": "^1.0.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-formatter-github": "^1.1.3", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.0", "is-ci": "^3.0.1", "jest": "29.6.2", "jest-junit": "^15.0.0", "nest-commander-testing": "^3.1.0", "pkg": "^5.8.1", "prettier": "^2.3.2", "semantic-release": "^20.1.3", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.1.1", "ts-loader": "^9.2.3", "tsconfig-paths": "4.2.0", "typescript": "^4.9.5"}, "jest": {"preset": "ts-jest/presets/default-esm", "moduleFileExtensions": ["js", "json", "ts"], "roots": ["<rootDir>/src/", "<rootDir>/test/"], "testRegex": [".*\\.spec\\.ts$", ".*\\.e2e-spec\\.ts$"], "extensionsToTreatAsEsm": [".ts"], "moduleNameMapper": {"^(\\.{1,2}/.*)\\.js$": "$1"}, "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"useESM": true}]}, "collectCoverageFrom": ["**/src/**/*.(t|j)s", "!**/src/repl.*"], "coverageDirectory": "<rootDir>/coverage/", "testEnvironment": "node", "reporters": ["default", "jest-junit"]}, "jest-junit": {"reportTestSuiteErrors": true}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "publishConfig": {"access": "public"}, "bin": {"lyricstify": "dist/cli.js"}, "repository": {"type": "git", "url": "git+https://github.com/lyricstify/lyricstify.git"}, "bugs": {"url": "https://github.com/lyricstify/lyricstify/issues"}, "homepage": "https://github.com/lyricstify/lyricstify#readme", "pkg": {"outputPath": "executable", "targets": ["node18-linux-x64", "node18-macos-x64", "node18-win-x64"], "assets": ["node_modules/terminal-kit/**/*", "node_modules/kuromoji/**/*", "!node_modules/kuromoji/test/**/*"]}}