name: Run Code Tests

on:
  workflow_dispatch:
  push:
    branches:
      - '*.x'
      - main
      - next
      - next-major
      - beta
      - alpha
      - dev
  pull_request:
    branches:
      - '*.x'
      - main
      - next
      - next-major
      - beta
      - alpha
      - dev

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node: [16, 18]

    name: Node ${{ matrix.node }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: npm

      - name: Clean install dependencies
        run: npm ci

      - name: Run tests and collect coverage
        run: npm run test:ci

      - name: Publish test report
        uses: mikepenz/action-junit-report@v4
        if: ${{ github.event_name == 'pull_request' && always() }}
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          report_paths: junit.xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          fail_ci_if_error: true
