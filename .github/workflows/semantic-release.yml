name: Run Auto Release Package using Semantic Versioning
on:
  workflow_dispatch:
  workflow_run:
    workflows: [Run Code Tests]
    types:
      - completed

jobs:
  release:
    if: ${{ github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.event == 'push' }}

    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 19
          cache: npm

      - name: Clean install dependencies
        run: npm ci

      - name: Build package
        run: |
          npm run build
          npm run build:bundle
          npm run build:executable

      - name: Release
        uses: nick-fields/retry@v3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        with:
          max_attempts: 3
          timeout_seconds: 600
          retry_on: error
          retry_wait_seconds: 300
          command: npx semantic-release
