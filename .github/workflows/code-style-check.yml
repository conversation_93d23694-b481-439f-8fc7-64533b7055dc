name: Run Code Style Checks

on:
  workflow_dispatch:
  pull_request:
    branches:
      - '*.x'
      - main
      - next
      - next-major
      - beta
      - alpha
      - dev
    paths:
      - src/**/*.ts
      - test/**/*.ts

jobs:
  code-style-check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 19
          cache: npm

      - name: Clean install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint:ci
