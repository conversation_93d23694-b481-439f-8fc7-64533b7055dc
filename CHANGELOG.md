## [1.1.2](https://github.com/lyricstify/lyricstify/compare/v1.1.1...v1.1.2) (2023-05-14)


### Bug Fixes

* **deps:** bump ora from 6.1.2 to 6.3.0 ([23b3ef6](https://github.com/lyricstify/lyricstify/commit/23b3ef6bbf6b32e0d0824660d2e30163ec507d0a))

## [1.1.1](https://github.com/lyricstify/lyricstify/compare/v1.1.0...v1.1.1) (2023-04-29)


### Bug Fixes

* **player:** improve http error handling in currently playing ([d3860a7](https://github.com/lyricstify/lyricstify/commit/d3860a7086fe80482b7398fb04481b958e2e7e72))
* **transformation:** update deps to google-translate-api-x for better stability ([af00d84](https://github.com/lyricstify/lyricstify/commit/af00d84264b5bcf65590e14199f99ae9aa02d9b6))


### Performance Improvements

* **token:** reduce i/o to disk by cache entity to memory ([f219c84](https://github.com/lyricstify/lyricstify/commit/f219c842739f87ce5a9cb7371416ab7ffde9a8bf))

# [1.1.0](https://github.com/lyricstify/lyricstify/compare/v1.0.7...v1.1.0) (2023-04-19)


### Bug Fixes

* **command-validation:** allow to use 0 vertical spacing ([0da5b6c](https://github.com/lyricstify/lyricstify/commit/0da5b6c92a211edb1a85a2b66181dfdf454bef01))
* **command-validation:** improve vertical spacing error description ([01a8068](https://github.com/lyricstify/lyricstify/commit/01a80684bed76c78da64481edc9d0d98c81cf2de))
* **start:** update active lyrics position calculation ([6c37e9e](https://github.com/lyricstify/lyricstify/commit/6c37e9e03d010ffe1924df858d79b2950f81c4db))


### Features

* **transformation:** add options to hide source lyrics ([69d5543](https://github.com/lyricstify/lyricstify/commit/69d554350296749dfa9ed16f2ef871c9310b20f3))

## [1.0.7](https://github.com/lyricstify/lyricstify/compare/v1.0.6...v1.0.7) (2023-04-13)


### Bug Fixes

* **transformation:** change steps to combine lyrics and translation ([7eaf5d0](https://github.com/lyricstify/lyricstify/commit/7eaf5d0489dfde17f4c82d3212aed2dab128fa91))

## [1.0.6](https://github.com/lyricstify/lyricstify/compare/v1.0.5...v1.0.6) (2023-04-05)


### Bug Fixes

* **terminal-link:** remove hyperlinks from the terminal link and display the URL instead ([38a2baa](https://github.com/lyricstify/lyricstify/commit/38a2baae4a031536bcf649469f1b27ac2e33adda))

## [1.0.5](https://github.com/lyricstify/lyricstify/compare/v1.0.4...v1.0.5) (2023-04-02)


### Bug Fixes

* **cli:** add node shebang ([8185539](https://github.com/lyricstify/lyricstify/commit/81855393e2a45abd7e09f4ee40deae0933c27b71))

## [1.0.4](https://github.com/lyricstify/lyricstify/compare/v1.0.3...v1.0.4) (2023-04-02)


### Bug Fixes

* **player:** change expired token error handling ([027fd55](https://github.com/lyricstify/lyricstify/commit/027fd55bd65d3295eb5bc6e2859b1d4e1e09f807))
