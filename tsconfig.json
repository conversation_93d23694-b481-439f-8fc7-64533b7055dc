{"compilerOptions": {"module": "nodenext", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": false, "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": false, "esModuleInterop": true}}