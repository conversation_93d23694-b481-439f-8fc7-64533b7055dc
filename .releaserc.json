{"branches": ["+([0-9])?(.{+([0-9]),x}).x", "main", "next", "next-major", {"name": "beta", "prerelease": true}, {"name": "alpha", "prerelease": true}, {"name": "dev", "prerelease": true}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], "@semantic-release/npm", ["@semantic-release/github", {"assets": [{"path": "executable/@lyricstify/lyricstify-linux", "label": "lyricstify-linux-x64"}, {"path": "executable/@lyricstify/lyricstify-macos", "label": "lyricstify-macos-x64"}, {"path": "executable/@lyricstify/lyricstify-win.exe", "label": "lyricstify-win-x64"}]}], ["@semantic-release/git", {"assets": ["package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}